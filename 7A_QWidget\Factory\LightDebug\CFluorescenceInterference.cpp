#include "CFluorescenceInterference.h"
#include <QTimer>
#include <QDebug>
#include <QListView>
#include <QBoxLayout>
#include <QHeaderView>
#include "CMessageBox.h"
#include "CLightOneTiming.h"
#include "MDControl/CReadWriteXlsxThread.h"

CFluorescenceInterference::CFluorescenceInterference(QWidget *parent) : QWidget(parent)
{
    QTime t1 = QTime::currentTime();


    Register2Map(Method_FLMDT);

    m_pLightOneTiming = new CLightOneTiming();
    connect(m_pLightOneTiming, &CLightOneTiming::SignalTimingEnd, this, &CFluorescenceInterference::_SlotTimingEnd);
    _InitWidget();
    bInterfereTested = false;
    m_pCBusyProgressBar = new CBusyProgressBar(gk_strTipsText, tr("正在进行荧光干扰测试"));

    qDebug()<<"光学性能测试页面构造时间:"<<t1.msecsTo(QTime::currentTime());
}

CFluorescenceInterference::~CFluorescenceInterference()
{
    UnRegister2Map(Method_FLMDT);
}

void CFluorescenceInterference::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if (bInterfereTested)
    {
        if(iMethodID == Method_FLMDT) {
            _ReceiveMDTData(qVarData);
        }
    }
}

void CFluorescenceInterference::ClearAllData(void)
{
    m_flHole1Vlist.clear();
    m_flHole2Vlist.clear();
    bInterfereTested = false;
    for (int i = 0; i < 4; i++) {
        _SetTableItem(i, 1, tr(""));
        _SetTableItem(i, 2, tr("")); 
    }
}

void CFluorescenceInterference::SetFluorescenceType(int iTypeIndex)
{
    m_iFluorescenceType = iTypeIndex;
}

void CFluorescenceInterference::_SlotTimingEnd(void)
{
    qDebug()<<"_SlotTimingEnd is called";
    bool bHole1Passed = true;
    //判断m_flHole1Vlist里面对应每个Hole的value[m_colorIndex]是否是整个hole数组中最大的
    for (int i = 0; i < m_flHole1Vlist.size(); i++) {
        double max = m_flHole1Vlist.at(i).value[m_colorIndex];
        int maxIndex = m_colorIndex;
        for (int j = 0; j < 4; j++) {
            if (m_flHole1Vlist.at(i).value[j] > max) {
                max = m_flHole1Vlist.at(i).value[j];
                maxIndex = j;
            } 
        }
        if (maxIndex != m_colorIndex) {
            bHole1Passed = false;
            break;
        }
    }
    if (bHole1Passed) {
        qDebug()<<tr("Color %1 孔1荧光干扰测试通过").arg(m_colorIndex);
        _SetTableItem(m_colorIndex, 1, tr("通过"));
    }
    else {
        qDebug()<<tr("Color %1 孔1荧光干扰测试失败").arg(m_colorIndex);
        _SetTableItem(m_colorIndex, 1, tr("失败")); 
        qDebug()<<"m_flHole1Vlist.size():"<<m_flHole1Vlist.size();
        for (int i = 0; i < m_flHole1Vlist.size(); i++) {
            qDebug()<<"m_flHole1Vlist.at(i):"<<m_flHole1Vlist.at(i).value[0]<<m_flHole1Vlist.at(i).value[1]<<m_flHole1Vlist.at(i).value[2]<<m_flHole1Vlist.at(i).value[3]; 
        }
    }

    bool bHole2Passed = true;
    //判断m_flHole2Vlist里面对应每个Hole的value[m_colorIndex]是否是整个hole数组中最大的
    for (int i = 0; i < m_flHole2Vlist.size(); i++) {
        double max = m_flHole2Vlist.at(i).value[m_colorIndex];
        int maxIndex = m_colorIndex; 
        for (int j = 0; j < 4; j++) {
            if (m_flHole2Vlist.at(i).value[j] > max) { 
                max = m_flHole2Vlist.at(i).value[j];
                maxIndex = j;
            } 
        }
        if (maxIndex!= m_colorIndex) {
            bHole2Passed = false;
            break; 
        }
    }
    if (bHole2Passed) {
        qDebug()<<tr("Color %1 孔2荧光干扰测试通过").arg(m_colorIndex); 
        _SetTableItem(m_colorIndex, 2, tr("通过"));
    }
    else {
        qDebug()<<tr("Color %1 孔2荧光干扰测试失败").arg(m_colorIndex);
        qDebug()<<"m_flHole2Vlist.size():"<<m_flHole2Vlist.size();
        for (int i = 0; i < m_flHole2Vlist.size(); i++) {
            qDebug()<<"m_flHole2Vlist.at(i):"<<m_flHole2Vlist.at(i).value[0]<<m_flHole2Vlist.at(i).value[1]<<m_flHole2Vlist.at(i).value[2]<<m_flHole2Vlist.at(i).value[3]; 
        }
        _SetTableItem(m_colorIndex, 2, tr("失败")); 
    }
    _SaveXlsxData(m_colorIndex);
    m_pCBusyProgressBar->close();

    bInterfereTested = false;
}

bool CFluorescenceInterference::_SaveXlsxData(int colorIndex)
{
    QString strXlsxDir = CPublicConfig::GetInstance()->GetXlsxDir();
    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxDir + CPublicConfig::GetInstance()->GetLightSNXlsxName();
    QString strTempXlsxName = "";
    if (m_iFluorescenceType == 1) {
        strTempXlsxName = "FlToolData-dye-template.xlsx";
    }
    else {
        strTempXlsxName = "FlToolData-template.xlsx";
    }
    //先判断Xlsx目录下有没有当前SN的xlsx文件
    if (!QFile::exists(pXlsxStruct->strXlsxName)) {
        //如果没有,从Resource目录下复制FlToolData-template.xlsx模板文件到xlsx目录并改名成当前SN的xlsx文件
        QString strResourceDir = CPublicConfig::GetInstance()->GetResourceDir();
        QString strTempXlsxPath = strResourceDir + strTempXlsxName;
        if (!QFile::exists(strTempXlsxPath)) {
            ShowError(nullptr, gk_strTipsText, tr("Resource目录下没有%1模板文件").arg(strTempXlsxName));
            qDebug()<<QString("Resource目录下没有%1模板文件").arg(strTempXlsxName);
            return false;
        }
        QFile::copy(strTempXlsxPath, pXlsxStruct->strXlsxName);
        if (!QFile::exists(pXlsxStruct->strXlsxName)) {
            ShowError(nullptr, gk_strTipsText, tr("复制%1模板文件到xlsx目录失败").arg(strTempXlsxName));
            qDebug()<<QString("复制%1模板文件到xlsx目录失败").arg(strTempXlsxName);
            return false;
        }
    }
    
    pXlsxStruct->strTableName = tr("荧光测试");
    pXlsxStruct->bDrawChart = false;
    XlsxDataStruct xlsxDataStruct;
    for (int i = 0; i < 4; i++) {
        xlsxDataStruct.iRow = 64 + colorIndex;
        xlsxDataStruct.iColumn = 4 + i;
        xlsxDataStruct.varValue = m_flHole1Vlist.at(0).value[i];
        pXlsxStruct->dataStructList.append(xlsxDataStruct);
    }
    for (int i = 0; i < 4; i++) {
        xlsxDataStruct.iRow = 64 + colorIndex;
        xlsxDataStruct.iColumn = 20 + i;
        xlsxDataStruct.varValue = m_flHole2Vlist.at(0).value[i]; 
        pXlsxStruct->dataStructList.append(xlsxDataStruct);
    }
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);

    return true;
}

void CFluorescenceInterference::_SlotMachineChanged(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

}

void CFluorescenceInterference::_ReceiveMDTData(const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.size() < 5)
        return;

    int iHole = qVarList.at(0).toInt(); // 0; 1
    if(iHole < 0 || iHole >= gk_iHoleCount)
        return;

    sHoleFl_t sHoleFl;
    for (int i = 0; i < 4; i++) {
        sHoleFl.value[i] = qVarList.at(i + 1).toDouble();
    }

    if (iHole == 0) {
        m_flHole1Vlist.push_back(sHoleFl);
    }
    else {
        m_flHole2Vlist.push_back(sHoleFl);
    }
}

void CFluorescenceInterference::_SlotFamMDTBtn()
{
    bInterfereTested = true;
    m_colorIndex = LIGHT_COLOR_FAM_INDEX;
    qDebug()<<"_SlotFamLinear is called";

    //清除hole1VList和hole2VList
    m_flHole1Vlist.clear();
    m_flHole2Vlist.clear();
    m_pCBusyProgressBar->show();
    m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 10);
}
void CFluorescenceInterference::_SlotHexMDTBtn()
{
    bInterfereTested = true;
    m_colorIndex = LIGHT_COLOR_HEX_INDEX;
    qDebug()<<"_SlotHexLinear is called";

    //清除hole1VList和hole2VList
    m_flHole1Vlist.clear();
    m_flHole2Vlist.clear();
    m_pCBusyProgressBar->show();
    m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 10);
}

void CFluorescenceInterference::_SlotRoxMDTBtn()
{
    bInterfereTested = true;
    m_colorIndex = LIGHT_COLOR_ROX_INDEX;
    qDebug()<<"_SlotRoxLinear is called";

    //清除hole1VList和hole2VList
    m_flHole1Vlist.clear();
    m_flHole2Vlist.clear();
    m_pCBusyProgressBar->show();
    m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 10);
}

void CFluorescenceInterference::_SlotCY5MDTBtn()
{
    bInterfereTested = true;
    m_colorIndex = LIGHT_COLOR_CY5_INDEX;
    qDebug()<<"_SlotCY5Linear is called";

    //清除hole1VList和hole2VList
    m_flHole1Vlist.clear();
    m_flHole2Vlist.clear();
    m_pCBusyProgressBar->show();
    m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 10);
}

void CFluorescenceInterference::_SetTableItem(int iRow, int iCol, QString strText)
{
    QTableWidgetItem *pItem = m_pTableWidget->item(iRow, iCol);
    if(nullptr != pItem)
    {
        pItem->setText(strText);
        return;
    }
    pItem = new QTableWidgetItem;
    pItem->setText(strText);
    pItem->setTextAlignment(Qt::AlignCenter);
    m_pTableWidget->setItem(iRow, iCol, pItem);
}

void CFluorescenceInterference::_SetTableWidget(int iRow, int iCol, int iWidth, int iHeight, QWidget *pUIWidget)
{
    QWidget *pCellWidget = new QWidget;
    pCellWidget->setFixedSize(iWidth, iHeight);
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(pUIWidget, 0, Qt::AlignVCenter);
    pLayout->addStretch(1);
    pCellWidget->setLayout(pLayout);
    m_pTableWidget->setCellWidget(iRow, iCol, pCellWidget);
}

void CFluorescenceInterference::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    //m_pStackedWidget = new QStackedWidget;
    // const QStringList strRatioList = {tr("25%"), tr("40%"), tr("60%"), tr("75%"), tr("90%")};
    // m_pRatioComboBox = new CLabelComboBox(tr("光衰减度:"), strRatioList);
    // m_pRatioComboBox->SetComboBoxFixedSize(80, 50);
    // connect(m_pRatioComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotRatioChanged(int)));
    
    // 新增四个采光按钮
    m_pFamMDTBtn = new QPushButton(tr("FAM采光"));
    m_pHexMDTBtn = new QPushButton(tr("HEX采光"));
    m_pRoxMDTBtn = new QPushButton(tr("ROX采光"));
    m_pCY5MDTBtn = new QPushButton(tr("CY5采光"));

    // 设置按钮固定尺寸
    m_pFamMDTBtn->setFixedSize(100, 40);
    m_pHexMDTBtn->setFixedSize(100, 40);
    m_pRoxMDTBtn->setFixedSize(100, 40);
    m_pCY5MDTBtn->setFixedSize(100, 40);

    // 连接按钮的点击信号到对应的槽函数
    connect(m_pFamMDTBtn, &QPushButton::clicked, this, &CFluorescenceInterference::_SlotFamMDTBtn);
    connect(m_pHexMDTBtn, &QPushButton::clicked, this, &CFluorescenceInterference::_SlotHexMDTBtn);
    connect(m_pRoxMDTBtn, &QPushButton::clicked, this, &CFluorescenceInterference::_SlotRoxMDTBtn);
    connect(m_pCY5MDTBtn, &QPushButton::clicked, this, &CFluorescenceInterference::_SlotCY5MDTBtn);

    QStringList strTitleList;
    strTitleList << tr("通道") << tr("孔1结果") << tr("孔2结果") <<"";

    m_pTableWidget = new QTableWidget(this);
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
    m_pTableWidget->setRowCount(4);
    m_pTableWidget->setFixedSize(600, 290);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(60);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 150);
    pHorizontalHeader->resizeSection(1, 150);
    pHorizontalHeader->resizeSection(2, 150);
    pHorizontalHeader->resizeSection(3, 150);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    _SetTableItem(0, 0, tr("FAM"));
    _SetTableItem(1, 0, tr("HEX"));
    _SetTableItem(2, 0, tr("ROX"));
    _SetTableItem(3, 0, tr("CY5"));

    _SetTableWidget(0, 3, 150, 60, m_pFamMDTBtn);
    _SetTableWidget(1, 3, 150, 60, m_pHexMDTBtn);
    _SetTableWidget(2, 3, 150, 60, m_pRoxMDTBtn);
    _SetTableWidget(3, 3, 150, 60, m_pCY5MDTBtn);

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->addStretch(1); // 添加伸缩项实现左对齐

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(10);
    pLayout->addLayout(pTopLayout);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pTableWidget, 0, Qt::AlignCenter);
    pLayout->addStretch(1); // 添加伸缩项实现左对齐

    this->setLayout(pLayout);
}
