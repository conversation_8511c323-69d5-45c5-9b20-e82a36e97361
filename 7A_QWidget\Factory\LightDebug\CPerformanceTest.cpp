#include "CPerformanceTest.h"
#include <QTimer>
#include <QDebug>
#include <QListView>
#include <QBoxLayout>
#include <QHeaderView>
#include <QFile>
#include "CMessageBox.h"
#include "CLightOneTiming.h"
#include <cmath>
#include "MDControl/CReadWriteXlsxThread.h"


//定义光衰减度常量
const QList <double> gk_dLightRange  = {0.9, 0.75, 0.6, 0.4, 0.25};
//定义染料梯度常量
const QList <double> gk_dDyeLightRange = {200, 100, 50, 25, 12.5};

CPerformanceTest::CPerformanceTest(QWidget *parent) : QWidget(parent)
{
    QTime t1 = QTime::currentTime();

    Register2Map(Method_FLMDT);

    m_pLightOneTiming = new CLightOneTiming();
    connect(m_pLightOneTiming, &CLightOneTiming::SignalTimingEnd, this, &CPerformanceTest::_SlotTimingEnd);
    
    m_iFluorescenceType = 0;  // 默认为荧光片模式
    m_ratioIndex = 0;
    _InitWidget();
    m_pCBusyProgressBar = new CBusyProgressBar(gk_strTipsText, tr("正在进行光学性能测试"));
    
    ClearAllData();

    qDebug()<<"光学性能测试页面构造时间:"<<t1.msecsTo(QTime::currentTime());
}

CPerformanceTest::~CPerformanceTest()
{
    UnRegister2Map(Method_FLMDT);
}

void CPerformanceTest::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if (m_bLinearTest)
    {
        if(iMethodID == Method_FLMDT) {
            _ReceiveMDTData(qVarData);
        }
    }
}

bool CPerformanceTest::_SaveXlsxData(int colorIndex, int ratioIndex)
{
    QString strXlsxDir = CPublicConfig::GetInstance()->GetXlsxDir();
    STXlsxParmasStruct *pXlsxStruct = new STXlsxParmasStruct;
    pXlsxStruct->strXlsxName = strXlsxDir + CPublicConfig::GetInstance()->GetLightSNXlsxName();
    QString strTempXlsxName = "";
    if (m_iFluorescenceType == 1) {
        strTempXlsxName = "FlToolData-dye-template.xlsx";
    }
    else {
        strTempXlsxName = "FlToolData-template.xlsx";
    }
    //先判断Xlsx目录下有没有当前SN的xlsx文件
    if (!QFile::exists(pXlsxStruct->strXlsxName)) {
        //如果没有,从Resource目录下复制FlToolData-template.xlsx模板文件到xlsx目录并改名成当前SN的xlsx文件
        QString strResourceDir = CPublicConfig::GetInstance()->GetResourceDir();
        QString strTempXlsxPath = strResourceDir + strTempXlsxName;
        if (!QFile::exists(strTempXlsxPath)) {
            ShowError(nullptr, gk_strTipsText, tr("Resource目录下没有%1模板文件").arg(strTempXlsxName));
            qDebug()<<QString("Resource目录下没有%1模板文件").arg(strTempXlsxName);
            return false;
        }
        QFile::copy(strTempXlsxPath, pXlsxStruct->strXlsxName);
        if (!QFile::exists(pXlsxStruct->strXlsxName)) {
            ShowError(nullptr, gk_strTipsText, tr("复制%1模板文件到xlsx目录失败").arg(strTempXlsxName));
            qDebug()<<QString("复制%1模板文件到xlsx目录失败").arg(strTempXlsxName);
            return false;
        }
    }

    pXlsxStruct->strTableName = tr("汇总数据");
    pXlsxStruct->bDrawChart = false;
    XlsxDataStruct xlsxDataStruct;
    for (int i = 0; i < m_flData[colorIndex][ratioIndex].hole1VList.size(); i++) {
        xlsxDataStruct.iRow = i + 3 + ratioIndex*10;
        xlsxDataStruct.iColumn = 2 + colorIndex;
        xlsxDataStruct.varValue = m_flData[colorIndex][ratioIndex].hole1VList.at(i);
        pXlsxStruct->dataStructList.append(xlsxDataStruct);
    }
    for (int i = 0; i < m_flData[colorIndex][ratioIndex].hole2VList.size(); i++) {
        xlsxDataStruct.iRow = i + 3 + ratioIndex*10;
        xlsxDataStruct.iColumn = 6 + colorIndex;
        xlsxDataStruct.varValue = m_flData[colorIndex][ratioIndex].hole2VList.at(i); 
        pXlsxStruct->dataStructList.append(xlsxDataStruct);
    }
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct);


    //将m_flHole1AllDataVlist和m_flHole2AllDataVlist保存到原始数据工作表中
    STXlsxParmasStruct *pXlsxStruct2 = new STXlsxParmasStruct;

    pXlsxStruct2->strXlsxName = strXlsxDir + CPublicConfig::GetInstance()->GetLightSNXlsxName();
    QString strTempXlsxName2 = "";
    if (m_iFluorescenceType == 1) {
        strTempXlsxName2 = "FlToolData-dye-template.xlsx";
    }
    else {
        strTempXlsxName2 = "FlToolData-template.xlsx";
    }
    //先判断Xlsx目录下有没有当前SN的xlsx文件
    if (!QFile::exists(pXlsxStruct2->strXlsxName)) {
        //如果没有,从Resource目录下复制FlToolData-template.xlsx模板文件到xlsx目录并改名成当前SN的xlsx文件
        QString strResourceDir = CPublicConfig::GetInstance()->GetResourceDir();
        QString strTempXlsxPath = strResourceDir + strTempXlsxName2;
        if (!QFile::exists(strTempXlsxPath)) {
            ShowError(nullptr, gk_strTipsText, tr("Resource目录下没有%1模板文件").arg(strTempXlsxName2));
            qDebug()<<QString("Resource目录下没有%1模板文件").arg(strTempXlsxName2);
            return false;
        }
        QFile::copy(strTempXlsxPath, pXlsxStruct2->strXlsxName);
        if (!QFile::exists(pXlsxStruct2->strXlsxName)) {
            ShowError(nullptr, gk_strTipsText, tr("复制%1模板文件到xlsx目录失败").arg(strTempXlsxName2));
            qDebug()<<QString("复制%1模板文件到xlsx目录失败").arg(strTempXlsxName2);
            return false;
        }
    }
    pXlsxStruct2->strTableName = tr("原始数据");
    pXlsxStruct2->bDrawChart = false;
    XlsxDataStruct xlsxDataStruct2;
    for (int i = 0; i < m_flHole1AllDataVlist.size(); i++) {
        for (int j = 0; j < 4; j++) {
            xlsxDataStruct2.iRow = i + 3 + colorIndex*10 + ratioIndex*40;
            xlsxDataStruct2.iColumn = 2 + j;
            xlsxDataStruct2.varValue = m_flHole1AllDataVlist.at(i).value[j];
            pXlsxStruct2->dataStructList.append(xlsxDataStruct2);
        }
    }
    for (int i = 0; i < m_flHole2AllDataVlist.size(); i++) {
        for (int j = 0; j < 4; j++) {
            xlsxDataStruct2.iRow = i + 3 + colorIndex*10 + ratioIndex*40;
            xlsxDataStruct2.iColumn = 6 + j;
            xlsxDataStruct2.varValue = m_flHole2AllDataVlist.at(i).value[j];
            pXlsxStruct2->dataStructList.append(xlsxDataStruct2);
        }
    }
    m_flHole1AllDataVlist.clear();
    m_flHole2AllDataVlist.clear();
    CReadWriteXlsxThread::GetInstance()->AddXlsxParamsStruct(pXlsxStruct2);

    return true;
}

void CPerformanceTest::_SlotTimingEnd(void)
{
    qDebug()<<"_SlotTimingEnd is called";
    //计算当前hole1VList和hole2VList的平均值
    qDebug()<<"hole1Vlist:"<<m_flData[m_colorIndex][m_ratioIndex].hole1VList<<"hole2Vlist:"<<m_flData[m_colorIndex][m_ratioIndex].hole2VList;
    double hole1Average = 0;
    double hole2Average = 0;

    m_bLinearTest = false;
    for(int i = 0; i < m_flData[m_colorIndex][m_ratioIndex].hole1VList.size(); i++) {
        hole1Average += m_flData[m_colorIndex][m_ratioIndex].hole1VList.at(i);
    }
    for(int i = 0; i < m_flData[m_colorIndex][m_ratioIndex].hole2VList.size(); i++) {
        hole2Average += m_flData[m_colorIndex][m_ratioIndex].hole2VList.at(i);
    }
    hole1Average = hole1Average / m_flData[m_colorIndex][m_ratioIndex].hole1VList.size();
    hole2Average = hole2Average / m_flData[m_colorIndex][m_ratioIndex].hole2VList.size();
    m_flData[m_colorIndex][m_ratioIndex].hole1Average = hole1Average;
    m_flData[m_colorIndex][m_ratioIndex].hole2Average = hole2Average;

    //计算标准差P
    double stdevP1 = calculateStdevP(m_flData[m_colorIndex][m_ratioIndex].hole1VList);
    m_flData[m_colorIndex][m_ratioIndex].hole1StdevP = stdevP1;
    double stdevP2 = calculateStdevP(m_flData[m_colorIndex][m_ratioIndex].hole2VList);
    m_flData[m_colorIndex][m_ratioIndex].hole2StdevP = stdevP2;
    qDebug()<<"stdevP1:"<<stdevP1<<"stdevP2:"<<stdevP2;

    //计算CV
    double cv = stdevP1 / hole1Average;
    m_flData[m_colorIndex][m_ratioIndex].hole1Cv = cv;
    cv = stdevP2 / hole2Average;
    m_flData[m_colorIndex][m_ratioIndex].hole2Cv = cv;

    //把两个孔的数据都混合到一起计算标准差和CV
    QList <double> allHoleVList;
    for (int i = 0; i < m_flData[m_colorIndex][m_ratioIndex].hole1VList.size(); i++) {
        allHoleVList.append(m_flData[m_colorIndex][m_ratioIndex].hole1VList.at(i));
    }
    for (int i = 0; i < m_flData[m_colorIndex][m_ratioIndex].hole2VList.size(); i++) {
        allHoleVList.append(m_flData[m_colorIndex][m_ratioIndex].hole2VList.at(i));
    }
    double allHoleAverage = 0;
    for (double val : allHoleVList) {
        allHoleAverage += val;
    }
    allHoleAverage /= allHoleVList.size();
    double allHoleStdevP = calculateStdevP(allHoleVList);
    double allHoleCv = allHoleStdevP / allHoleAverage;
    m_flData[m_colorIndex][m_ratioIndex].allHoleCv = allHoleCv;

    // 只在高中低浓度（索引0,2,4）时计算并显示到精密度表格
    if (m_ratioIndex == 0 || m_ratioIndex == 2 || m_ratioIndex == 4) {
        // // 计算单孔重复性结果
        // bool hole1Repeatability = (m_flData[m_colorIndex][m_ratioIndex].hole1Cv < 0.03) && 
        //                            (m_flData[m_colorIndex][m_ratioIndex].hole2Cv < 0.03);
        // 计算双孔合并精密度结果
        bool precision = (allHoleCv < 0.05);
        
        // 确定在精密度表格中的列索引
        int colIndex = 0;
        if (m_ratioIndex == 0) {        // 高浓度
            colIndex = 1;  // 重复性列
        } else if (m_ratioIndex == 2) { // 中浓度
            colIndex = 3;  // 重复性列
        } else if (m_ratioIndex == 4) { // 低浓度
            colIndex = 5;  // 重复性列
        }
        //分别在对应表格里同时显示孔1和孔2的重复性结果，孔1通过，孔2不通过，String换行

        QString strHole1Repeatability = m_flData[m_colorIndex][m_ratioIndex].hole1Cv < 0.03 ? tr("孔1通过") : tr("孔1不通过");
        QString strHole2Repeatability = m_flData[m_colorIndex][m_ratioIndex].hole2Cv < 0.03 ? tr("孔2通过") : tr("孔2不通过");
        QString strResult = strHole1Repeatability + "," + strHole2Repeatability;   

        // 显示到精密度表格
        _SetPrecisionTableItem(m_colorIndex, colIndex, strResult);
        _SetPrecisionTableItem(m_colorIndex, colIndex + 1, precision ? tr("通过") : tr("不通过"));
        
        qDebug()<<"浓度"<<m_ratioIndex<<"重复性:"<<strResult<<"精密度:"<<(precision ? "通过" : "不通过");
    }


    m_flData[m_colorIndex][m_ratioIndex].bIsTested = true;
    //显示当前平均值
    qDebug()<<"hole1Average:"<<hole1Average<<"hole2Average:"<<hole2Average;
    _SetTableItem(m_colorIndex*2, m_ratioIndex+1, QString::number(hole1Average, 'f', 3));
    _SetTableItem(m_colorIndex*2+1, m_ratioIndex+1, QString::number(hole2Average, 'f', 3));

    //判断是否当前颜色所有比例都测试过
    bool bAllTested = true;
    for (int i = 0; i < 5; i++) {
        if (!m_flData[m_colorIndex][i].bIsTested) {
            bAllTested = false;
            break;
        } 
    }
    if (bAllTested) {
        //计算线性度
        double hole1Linearity = 0;
        double hole2Linearity = 0;
        QList <double> hole1AverageList;
        QList <double> hole2AverageList;
        for (int i = 0; i < 5; i++) {
            hole1AverageList.append(m_flData[m_colorIndex][i].hole1Average);
            hole2AverageList.append(m_flData[m_colorIndex][i].hole2Average);
        }
        if (m_iFluorescenceType == 1) {
            hole1Linearity = pearsonOptimized(gk_dDyeLightRange, hole1AverageList);
            hole2Linearity = pearsonOptimized(gk_dDyeLightRange, hole2AverageList);
        }
        else {
            hole1Linearity = pearsonOptimized(gk_dLightRange, hole1AverageList);
            hole2Linearity = pearsonOptimized(gk_dLightRange, hole2AverageList);
        }
        qDebug()<<"hole1Linearity:"<<hole1Linearity<<"hole2Linearity:"<<hole2Linearity;

        if (hole1Linearity > 0.99) {
            qDebug()<<tr("Color%1 hole1线性度大于0.99, 测试通过").arg(m_colorIndex);
            _SetTableItem(m_colorIndex*2, 6, tr("通过"));
        }
        else {
            qDebug()<<"hole1线性度小于0.99, 测试失败";
            ShowError(nullptr, gk_strTipsText, tr("hole1线性度小于0.99, 不通过")); 
            _SetTableItem(m_colorIndex*2, 6, tr("不通过"));
        }

        if (hole2Linearity > 0.99) {
            qDebug()<<tr("Color%1 hole2线性度大于0.99, 测试通过").arg(m_colorIndex);
            _SetTableItem(m_colorIndex*2+1, 6, tr("通过")); 
        }
        else {
            qDebug()<<"hole2线性度小于0.99, 测试失败";
            ShowError(nullptr, gk_strTipsText, tr("hole2线性度小于0.99, 不通过"));
            _SetTableItem(m_colorIndex*2+1, 6, tr("不通过")); 
        }
    }

    _SaveXlsxData(m_colorIndex, m_ratioIndex);

    m_pCBusyProgressBar->close();
}

void CPerformanceTest::_SlotMachineChanged(int iMachineID)
{
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

}

void CPerformanceTest::_SlotRatioChanged(int iRatioIndex)
{
    if(iRatioIndex < 0 || iRatioIndex >= 5)
        return;
    m_ratioIndex = iRatioIndex;
}

void CPerformanceTest::_ReceiveMDTData(const QVariant &qVarData)
{
    QVariantList qVarList = qVarData.toList();
    if(qVarList.size() < 5)
        return;

    int iHole = qVarList.at(0).toInt(); // 0; 1
    if(iHole < 0 || iHole >= gk_iHoleCount)
        return;

    if (iHole == 0) {
        m_flData[m_colorIndex][m_ratioIndex].hole1VList.append(qVarList.at(m_colorIndex+1).toDouble()); // 0
    }
    else {
        m_flData[m_colorIndex][m_ratioIndex].hole2VList.append(qVarList.at(m_colorIndex+1).toDouble()); // 1 
    }

    //将每次采光数据保存到m_flHole1AllDataVlist和m_flHole2AllDataVlist
    sHoleFl_t holeFl;
    for (int i = 0; i < 4; i++) {
        holeFl.value[i] = qVarList.at(i+1).toDouble();
    }
    if (iHole == 0) {
        m_flHole1AllDataVlist.append(holeFl);
    }
    else {
        m_flHole2AllDataVlist.append(holeFl);
    }
}

void CPerformanceTest::_SlotFamMDTBtn()
{
    m_bLinearTest = true;
    m_colorIndex = LIGHT_COLOR_FAM_INDEX;
    qDebug()<<"_SlotFamLinear is called";

    //清除hole1VList和hole2VList
    m_flData[m_colorIndex][m_ratioIndex].hole1VList.clear();
    m_flData[m_colorIndex][m_ratioIndex].hole2VList.clear();
    m_flHole1AllDataVlist.clear();
    m_flHole2AllDataVlist.clear();
    m_pCBusyProgressBar->show();
    m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 10);
}
void CPerformanceTest::_SlotHexMDTBtn()
{
    m_bLinearTest = true;
    m_colorIndex = LIGHT_COLOR_HEX_INDEX;
    qDebug()<<"_SlotHexLinear is called";

    //清除hole1VList和hole2VList
    m_flData[m_colorIndex][m_ratioIndex].hole1VList.clear();
    m_flData[m_colorIndex][m_ratioIndex].hole2VList.clear();
    m_flHole1AllDataVlist.clear();
    m_flHole2AllDataVlist.clear();
    m_pCBusyProgressBar->show();
    m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 10);
}

void CPerformanceTest::_SlotRoxMDTBtn()
{
    m_bLinearTest = true;
    m_colorIndex = LIGHT_COLOR_ROX_INDEX;
    qDebug()<<"_SlotRoxLinear is called";

    //清除hole1VList和hole2VList
    m_flData[m_colorIndex][m_ratioIndex].hole1VList.clear();
    m_flData[m_colorIndex][m_ratioIndex].hole2VList.clear();
    m_flHole1AllDataVlist.clear();
    m_flHole2AllDataVlist.clear();
    m_pCBusyProgressBar->show();
    m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 10);
}

void CPerformanceTest::_SlotCY5MDTBtn()
{
    m_bLinearTest = true;
    m_colorIndex = LIGHT_COLOR_CY5_INDEX;
    qDebug()<<"_SlotCY5Linear is called";

    //清除hole1VList和hole2VList
    m_flData[m_colorIndex][m_ratioIndex].hole1VList.clear();
    m_flData[m_colorIndex][m_ratioIndex].hole2VList.clear();
    m_flHole1AllDataVlist.clear();
    m_flHole2AllDataVlist.clear();
    m_pCBusyProgressBar->show();
    m_pLightOneTiming->startTiming(m_pMachineComboBox->GetCurrentIndex(), 10);
}

void CPerformanceTest::ClearAllData()
{
    for (int i = 0; i < 4; i++) {
        for (int j = 0; j < 5; j++) {
            m_flData[i][j].hole1VList.clear();
            m_flData[i][j].hole2VList.clear();
            m_flData[i][j].hole1Average = 0;
            m_flData[i][j].hole2Average = 0;
            m_flData[i][j].bIsTested= false;
            m_flData[i][j].hole1StdevP = 0;
            m_flData[i][j].hole2StdevP = 0;
            m_flData[i][j].hole1Cv = 0;
            m_flData[i][j].hole2Cv = 0;
        }
    }

    //清空所有_SetTableItem
    for (int i = 0; i < 8; i++) {
        for (int j = 1; j < 7; j++) {
            _SetTableItem(i, j, "");
        }
    }
    
    //清空精密度表格
    for (int i = 0; i < 4; i++) {
        for (int j = 1; j < 7; j++) {
            _SetPrecisionTableItem(i, j, "");
        }
    }

    m_flHole1AllDataVlist.clear();
    m_flHole2AllDataVlist.clear();

    m_pRatioComboBox->SetCurrentIndex(0);
    m_bLinearTest = false;
}

void CPerformanceTest::_SetTableItem(int iRow, int iCol, QString strText)
{
    QTableWidgetItem *pItem = m_pTableWidget->item(iRow, iCol);
    if(nullptr != pItem)
    {
        pItem->setText(strText);
        return;
    }
    pItem = new QTableWidgetItem;
    pItem->setText(strText);
    pItem->setTextAlignment(Qt::AlignCenter);
    m_pTableWidget->setItem(iRow, iCol, pItem);
}

void CPerformanceTest::_SetPrecisionTableItem(int iRow, int iCol, QString strText)
{
    QTableWidgetItem *pItem = m_pPrecisionTableWidget->item(iRow, iCol);
    if(nullptr != pItem)
    {
        pItem->setText(strText);
        return;
    }
    pItem = new QTableWidgetItem;
    pItem->setText(strText);
    pItem->setTextAlignment(Qt::AlignCenter);
    m_pPrecisionTableWidget->setItem(iRow, iCol, pItem);
}

void CPerformanceTest::_SetTableWidget(int iRow, int iCol, int iWidth, int iHeight, QWidget *pUIWidget)
{
    QWidget *pCellWidget = new QWidget;
    pCellWidget->setFixedSize(iWidth, iHeight);
    QHBoxLayout *pLayout = new QHBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(0);
    pLayout->addStretch(1);
    pLayout->addWidget(pUIWidget, 0, Qt::AlignVCenter);
    pLayout->addStretch(1);
    pCellWidget->setLayout(pLayout);
    m_pTableWidget->setCellWidget(iRow, iCol, pCellWidget);
}

double CPerformanceTest::calculateStdevP(const QList<double>& data) {
    if (data.isEmpty()) return 1.0;

    // 计算均值
    double sum = 0.0;
    for (double val : data) {
        sum += val;
    }
    double mean = sum / data.size();

    // 计算平方差和
    double variance = 0.0;
    for (double val : data) {
        variance += std::pow(val - mean, 2);
    }
    variance /= data.size(); // 总体方差：除以N

    return std::sqrt(variance);
}

double CPerformanceTest::pearsonOptimized(const QList<double>& x, const QList<double>& y) {
    const int n = x.size();
    if (n != y.size() || n == 0) return NAN;

    double sum_x = 0, sum_y = 0;
    double sum_xy = 0, sum_x2 = 0, sum_y2 = 0;

    for (int i = 0; i < n; ++i) {
        sum_x += x[i];
        sum_y += y[i];
        sum_xy += x[i] * y[i];
        sum_x2 += x[i] * x[i];
        sum_y2 += y[i] * y[i];
    }

    const double numerator = n * sum_xy - sum_x * sum_y;
    const double denominator = sqrt(
        (n * sum_x2 - sum_x * sum_x) * 
        (n * sum_y2 - sum_y * sum_y)
    );

    return (denominator == 0) ? 0 : numerator / denominator;
}

void CPerformanceTest::_InitWidget()
{
    m_pMachineComboBox = new CLabelComboBox(tr("机器:"), gk_strMachineNameList);
    m_pMachineComboBox->SetComboBoxFixedSize(80, 50);
    connect(m_pMachineComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotMachineChanged(int)));

    //m_pStackedWidget = new QStackedWidget;
    const QStringList strRatioList = {tr("90%"), tr("75%"), tr("60%"), tr("40%"), tr("25%")};
    m_pRatioComboBox = new CLabelComboBox(tr("光衰减度:"), strRatioList);
    m_pRatioComboBox->SetComboBoxFixedSize(100, 50);
    connect(m_pRatioComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotRatioChanged(int)));
    
    // 新增四个采光按钮
    m_pFamMDTBtn = new QPushButton(tr("FAM采光"));
    m_pHexMDTBtn = new QPushButton(tr("HEX采光"));
    m_pRoxMDTBtn = new QPushButton(tr("ROX采光"));
    m_pCY5MDTBtn = new QPushButton(tr("CY5采光"));

    // 设置按钮固定尺寸
    m_pFamMDTBtn->setFixedSize(100, 40);
    m_pHexMDTBtn->setFixedSize(100, 40);
    m_pRoxMDTBtn->setFixedSize(100, 40);
    m_pCY5MDTBtn->setFixedSize(100, 40);

    // 连接按钮的点击信号到对应的槽函数
    connect(m_pFamMDTBtn, &QPushButton::clicked, this, &CPerformanceTest::_SlotFamMDTBtn);
    connect(m_pHexMDTBtn, &QPushButton::clicked, this, &CPerformanceTest::_SlotHexMDTBtn);
    connect(m_pRoxMDTBtn, &QPushButton::clicked, this, &CPerformanceTest::_SlotRoxMDTBtn);
    connect(m_pCY5MDTBtn, &QPushButton::clicked, this, &CPerformanceTest::_SlotCY5MDTBtn);

    QStringList strTitleList;
    strTitleList << tr("通道") << tr("90%") << tr("75%") << tr("60%") << tr("40%") << tr("25%") << tr("线性度") <<"";

    m_pTableWidget = new QTableWidget(this);
    m_pTableWidget->setColumnCount(strTitleList.size());
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
    m_pTableWidget->setRowCount(8);
    m_pTableWidget->setFixedSize(860, 450);

    QHeaderView* pVerticalHeader = m_pTableWidget->verticalHeader();
    pVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pHorizontalHeader = m_pTableWidget->horizontalHeader();
    pHorizontalHeader->resizeSection(0, 120);
    pHorizontalHeader->resizeSection(1, 100);
    pHorizontalHeader->resizeSection(2, 100);
    pHorizontalHeader->resizeSection(3, 100);
    pHorizontalHeader->resizeSection(4, 100);
    pHorizontalHeader->resizeSection(5, 100);
    pHorizontalHeader->resizeSection(6, 120);
    pHorizontalHeader->resizeSection(7, 120);

    m_pTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pTableWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pTableWidget->setShowGrid(true);
    m_pTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    _SetTableItem(0, 0, tr("孔1-FAM"));
    _SetTableItem(1, 0, tr("孔2-FAM"));
    _SetTableItem(2, 0, tr("孔1-HEX"));
    _SetTableItem(3, 0, tr("孔2-HEX"));
    _SetTableItem(4, 0, tr("孔1-ROX"));
    _SetTableItem(5, 0, tr("孔2-ROX"));
    _SetTableItem(6, 0, tr("孔1-CY5"));
    _SetTableItem(7, 0, tr("孔2-CY5"));

    m_pTableWidget->setSpan(0, 7, 2, 1);
    m_pTableWidget->setSpan(2, 7, 2, 1);
    m_pTableWidget->setSpan(4, 7, 2, 1);
    m_pTableWidget->setSpan(6, 7, 2, 1);

    _SetTableWidget(0, 7, 120, 100, m_pFamMDTBtn);
    _SetTableWidget(2, 7, 120, 100, m_pHexMDTBtn);
    _SetTableWidget(4, 7, 120, 100, m_pRoxMDTBtn);
    _SetTableWidget(6, 7, 120, 100, m_pCY5MDTBtn);

    // 创建精密度表格
    QStringList strPrecisionTitleList;
    strPrecisionTitleList << tr("通道") << tr("高浓度重复性") << tr("高浓度精密度") << tr("中浓度重复性") << tr("中浓度精密度") << tr("低浓度重复性") << tr("低浓度精密度");

    m_pPrecisionTableWidget = new QTableWidget(this);
    m_pPrecisionTableWidget->setColumnCount(strPrecisionTitleList.size());
    m_pPrecisionTableWidget->setHorizontalHeaderLabels(strPrecisionTitleList);
    m_pPrecisionTableWidget->setRowCount(4);
    m_pPrecisionTableWidget->setFixedSize(1150, 250);

    QHeaderView* pPrecisionVerticalHeader = m_pPrecisionTableWidget->verticalHeader();
    pPrecisionVerticalHeader->setDefaultSectionSize(50);
    QHeaderView* pPrecisionHorizontalHeader = m_pPrecisionTableWidget->horizontalHeader();
    pPrecisionHorizontalHeader->resizeSection(0, 100);
    pPrecisionHorizontalHeader->resizeSection(1, 200);
    pPrecisionHorizontalHeader->resizeSection(2, 150);
    pPrecisionHorizontalHeader->resizeSection(3, 200);
    pPrecisionHorizontalHeader->resizeSection(4, 150);
    pPrecisionHorizontalHeader->resizeSection(5, 200);
    pPrecisionHorizontalHeader->resizeSection(6, 150);

    m_pPrecisionTableWidget->setEditTriggers(QAbstractItemView::NoEditTriggers);
    m_pPrecisionTableWidget->setSelectionBehavior(QAbstractItemView::SelectRows);
    m_pPrecisionTableWidget->setSelectionMode(QAbstractItemView::NoSelection);
    m_pPrecisionTableWidget->setShowGrid(true);
    m_pPrecisionTableWidget->setFocusPolicy(Qt::NoFocus);
    m_pPrecisionTableWidget->setHorizontalScrollBarPolicy(Qt::ScrollBarAlwaysOff);
    m_pPrecisionTableWidget->setVerticalScrollBarPolicy(Qt::ScrollBarAlwaysOff);

    _SetPrecisionTableItem(0, 0, tr("FAM"));
    _SetPrecisionTableItem(1, 0, tr("HEX"));
    _SetPrecisionTableItem(2, 0, tr("ROX"));
    _SetPrecisionTableItem(3, 0, tr("CY5"));

    QHBoxLayout *pTopLayout = new QHBoxLayout;
    pTopLayout->setMargin(0);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pMachineComboBox);
    pTopLayout->setSpacing(20);
    pTopLayout->addWidget(m_pRatioComboBox);
    pTopLayout->addStretch(1); // 添加伸缩项实现左对齐

    QVBoxLayout *pLayout = new QVBoxLayout;
    pLayout->setMargin(0);
    pLayout->setSpacing(10);
    pLayout->addLayout(pTopLayout);
    pLayout->addSpacing(10);
    pLayout->addWidget(m_pTableWidget, 0, Qt::AlignCenter);
    pLayout->addSpacing(20);
    pLayout->addWidget(m_pPrecisionTableWidget, 0, Qt::AlignCenter);
    pLayout->addStretch(1); // 添加伸缩项实现左对齐

    this->setLayout(pLayout);
}

void CPerformanceTest::SetFluorescenceType(int iTypeIndex)
{
    m_iFluorescenceType = iTypeIndex;
    _UpdateRatioComboBoxAndTable();
}

void CPerformanceTest::_UpdateRatioComboBoxAndTable()
{
    // 根据荧光类型更新下拉框选项和表格标题
    QStringList strRatioList;
    QStringList strTitleList;
    QString strLabelText;
    
    if (m_iFluorescenceType == 1) // 染料模式
    {
        // 染料模式下显示浓度选项
        strRatioList = QStringList{tr("200nM"), tr("100nM"), tr("50nM"), tr("25nM"), tr("12.5nM")};
        strTitleList = QStringList{tr("通道"), tr("200nM"), tr("100nM"), tr("50nM"), tr("25nM"), tr("12.5nM"), tr("线性度"), ""};
        strLabelText = tr("浓度:");
    }
    else // 荧光片模式
    {
        // 荧光片模式下显示光衰减度选项
        strRatioList = QStringList{tr("90%"), tr("75%"), tr("60%"), tr("40%"), tr("25%")};
        strTitleList = QStringList{tr("通道"), tr("90%"), tr("75%"), tr("60%"), tr("40%"), tr("25%"), tr("线性度"), ""};
        strLabelText = tr("光衰减度:");
    }
    
    // 保存当前选中的索引
    int iCurrentIndex = m_pRatioComboBox->GetCurrentIndex();
    
    // 获取当前布局，准备重新创建下拉框
    QLayout* pTopLayout = layout()->itemAt(0)->layout();  // 假设顶部布局是第一个
    
    // 断开信号连接
    disconnect(m_pRatioComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotRatioChanged(int)));
    
    // 从布局中移除旧的下拉框
    pTopLayout->removeWidget(m_pRatioComboBox);
    delete m_pRatioComboBox;
    
    // 创建新的下拉框
    m_pRatioComboBox = new CLabelComboBox(strLabelText, strRatioList);
    m_pRatioComboBox->SetComboBoxFixedSize(100, 50);
    connect(m_pRatioComboBox, SIGNAL(SignalCurrentIndexChanged(int)), this, SLOT(_SlotRatioChanged(int)));
    
    // 将新的下拉框添加到布局中
    QHBoxLayout* pHBoxLayout = static_cast<QHBoxLayout*>(pTopLayout);
    pHBoxLayout->insertWidget(1, m_pRatioComboBox);  // 插入到机器下拉框后面
    
    // 恢复之前的选中索引
    if (iCurrentIndex >= 0 && iCurrentIndex < strRatioList.size()) {
        m_pRatioComboBox->SetCurrentIndex(iCurrentIndex);
    } else {
        m_pRatioComboBox->SetCurrentIndex(0);
    }
    
    // 更新表格标题
    m_pTableWidget->setHorizontalHeaderLabels(strTitleList);
}
