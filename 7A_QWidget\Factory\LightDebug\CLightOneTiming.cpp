#include <QTimer>
#include <QDebug>
#include "CLightOneTiming.h"
#include "CMessageBox.h"

const int cLightTiming[] = {Method_OFSTRST, Method_FLMST, Method_OFCOMP, Method_FLMSP};

CLightOneTiming::CLightOneTiming(QWidget *parent)
    : QWidget(parent)
{
    Register2Map(Method_OFSTRST);
    Register2Map(Method_OFCOMP);
    Register2Map(Method_FLMST);
    Register2Map(Method_FLMSP);

    m_lightTimingInfo.timingState = eLightTimingState_Idle;
}

CLightOneTiming::~CLightOneTiming()
{
    UnRegister2Map(Method_OFSTRST);
    UnRegister2Map(Method_OFCOMP);
    UnRegister2Map(Method_FLMST);
    UnRegister2Map(Method_FLMSP);
}

void CLightOneTiming::receiveMachineCmdReplay(int iMachineID, int iMethodID, int iResult, const QVariant &qVarData)
{
    
    if(iMachineID < 0 || iMachineID >= gk_iMachineCount)
        return;

    if (m_lightTimingInfo.timingState == eLightTimingState_WaitRet) {
        if(iMethodID == Method_OFSTRST) {
            if(iResult == 0) {
                timingSendNext(iMachineID);
            }
        }
        else if(iMethodID == Method_OFCOMP) {
            if(iResult == 0) {
                timingSendNext(iMachineID);
            } 
        }
        else if(iMethodID == Method_FLMST) {
            if(iResult == 0) {
                timingSendNext(iMachineID);
            } 
        }
        else if(iMethodID == Method_FLMSP) {
            if(iResult == 0) {
                timingSendNext(iMachineID);
            } 
        }
    }
}

void CLightOneTiming::startTiming(int iMachineID, int cycle)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index = 0;
    int iMethodID = cLightTiming[m_lightTimingInfo.index];
    QString strCmd = GetJsonCmdString(iMethodID);
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;
    m_lightTimingInfo.timingLength = sizeof(cLightTiming) / sizeof(cLightTiming[0]);
    m_lightTimingInfo.cycle = cycle;
    m_lightTimingInfo.cycleCount = 1;
}


void CLightOneTiming::timingSendNext(int iMachineID)
{
    m_lightTimingInfo.machineID = iMachineID;
    m_lightTimingInfo.index++;
    if (m_lightTimingInfo.index >= m_lightTimingInfo.timingLength) {
        
        if (m_lightTimingInfo.cycleCount < m_lightTimingInfo.cycle) {
            m_lightTimingInfo.cycleCount++;
            m_lightTimingInfo.index = 0;
        }
        else {
            m_lightTimingInfo.timingState = eLightTimingState_End;
            emit this->SignalTimingEnd();
            return;
        }
    }
    
    int iMethodID = cLightTiming[m_lightTimingInfo.index];
    QString strCmd;
    
    if (iMethodID == Method_FLMST) {
        QVariantList qVarList;
        qVarList.push_back(2);
        strCmd = GetJsonCmdString(iMethodID, qVarList);
    }
    else
    {
        strCmd = GetJsonCmdString(iMethodID); 
    }
    
    SendJsonCmd(m_lightTimingInfo.machineID, iMethodID, strCmd);
    m_lightTimingInfo.methodID = iMethodID;
    m_lightTimingInfo.bMethodReply = false;
    m_lightTimingInfo.timingState = eLightTimingState_WaitRet;

}